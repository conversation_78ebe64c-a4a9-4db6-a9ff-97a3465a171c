# 2023E（ZDT 米醋电控板）工程分析报告

## 项目概述

这是一个基于STM32F407VET6微控制器的嵌入式控制系统项目，主要用于机器人运动控制。从项目名称"ZDT 米醋电控板"可以看出，这是一个专门的电控板项目，可能用于自动化设备或机器人系统。

## 技术栈与开发环境

### 硬件平台
- **主控芯片**: STM32F407VET6 (ARM Cortex-M4, 168MHz)
- **开发板**: 基于STM32F407VET6的自定义电控板
- **外设接口**: 
  - 多路UART (UART1-6, UART4-5)
  - I2C (I2C1, I2C2)
  - 定时器 (TIM1, TIM3, TIM4)
  - DMA支持

### 开发环境
- **IDE**: Keil MDK-ARM V5.32
- **编译器**: ARMCC V5.06 update 7
- **HAL库**: STM32Cube FW_F4 V1.28.2
- **配置工具**: STM32CubeMX (.ioc文件)

## 项目架构分析

### 目录结构
```
2025template (2)/
├── Core/                    # STM32 HAL核心文件
│   ├── Inc/                # 头文件
│   └── Src/                # 源文件 (main.c等)
├── Drivers/                # STM32 HAL驱动库
├── MDK-ARM/                # Keil工程文件
├── OLED/                   # OLED显示模块
├── TB6612/                 # TB6612电机驱动模块
├── app/                    # 应用层代码
├── bsp/                    # 板级支持包
├── ringbuffer/             # 环形缓冲区
└── 2025template.ioc        # STM32CubeMX配置文件
```

### 软件架构层次

#### 1. 应用层 (app/)
- **motor_driver.c/h**: 电机驱动抽象层 (目前被注释，可能使用其他驱动方式)
- **encoder_drv.c/h**: 编码器驱动程序
- **hwt101_driver.c/h**: HWT101陀螺仪/IMU传感器驱动
- **Emm_V5.c/h**: Emm_V5步进电机控制器驱动
- **mypid.c/h**: PID控制算法实现
- **hardware_iic.c/h**: 硬件I2C通信接口

#### 2. 板级支持包 (bsp/)
- **schedule.c/h**: 任务调度系统
- **motor_bsp.c/h**: 电机底层驱动
- **encoder_bsp.c/h**: 编码器底层驱动
- **step_motor_bsp.c/h**: 步进电机底层驱动
- **hwt101_bsp.c/h**: HWT101传感器底层驱动
- **gray_bsp.c/h**: 灰度传感器驱动
- **pi_bsp.c/h**: 树莓派通信接口
- **uart_bsp.c/h**: UART通信底层驱动
- **oled_bsp.c/h**: OLED显示底层驱动
- **key_bsp.c/h**: 按键处理

#### 3. 硬件抽象层
- **TB6612/**: TB6612电机驱动芯片专用驱动
- **OLED/**: OLED显示屏驱动
- **ringbuffer/**: 环形缓冲区数据结构

## 核心功能模块分析

### 1. 任务调度系统 (schedule.c)
```c
// 任务调度表结构
typedef struct {
    void (*task_func)(void);    // 任务函数指针
    uint32_t rate_ms;          // 执行周期(毫秒)
    uint32_t last_run;         // 上次执行时间
} schedule_task_t;

// 当前激活的任务
static schedule_task_t schedule_task[] = {
    {uart_proc, 1, 0},         // UART处理，1ms周期
    {pi_proc, 20, 0}           // 树莓派通信处理，20ms周期
};
```

**特点**:
- 基于时间片的协作式多任务调度
- 支持不同任务的独立执行周期
- 轻量级实现，适合资源受限的嵌入式系统

### 2. PID控制系统 (mypid.c)
```c
// PID控制器实例
pid_t pid_x, pid_y;                    // 位置控制PID
pid_t pid_speed_left, pid_speed_right; // 速度控制PID
pid_t pid_location_left, pid_location_right; // 位置外环PID
```

**控制策略**:
- **双轴位置控制**: X轴和Y轴独立PID控制
- **双电机速度控制**: 左右电机独立速度环PID
- **位置-速度双环控制**: 外环位置控制，内环速度控制
- **支持增量式和位置式PID**: 可配置PID算法类型

### 3. 传感器系统

#### HWT101陀螺仪 (hwt101_driver.c)
- **功能**: 提供角速度和姿态角数据
- **通信**: UART串口通信
- **数据**: 角速度Z轴、偏航角(Yaw)
- **特性**: 支持配置保存、数据校准、状态管理

#### 编码器系统 (encoder_drv.c)
- **功能**: 电机位置和速度反馈
- **类型**: 可能是光电编码器或磁编码器
- **应用**: 闭环控制的位置和速度反馈

#### 灰度传感器 (gray_bsp.c)
- **功能**: 线路跟踪或位置检测
- **通信**: I2C接口
- **应用**: 可能用于路径跟踪或位置校准

### 4. 电机控制系统

#### 步进电机控制 (Emm_V5.c)
- **控制器**: Emm_V5步进电机控制器
- **通信**: UART串口控制
- **功能**: 
  - 位置清零
  - 参数读取
  - 运动控制
  - 状态监控

#### TB6612电机驱动 (TB6612/)
- **芯片**: TB6612FNG双路电机驱动
- **控制**: PWM + 方向控制
- **应用**: 直流电机驱动

### 5. 视觉系统接口 (pi_bsp.c)
```c
// 激光坐标结构
typedef struct {
    uint8_t laser_id;    // 激光ID (红色/绿色)
    int x, y;           // 坐标位置
    uint8_t isValid;    // 数据有效标志
} LaserCoord_t;

LaserCoord_t latest_red_laser_coord;   // 红色激光坐标
LaserCoord_t latest_green_laser_coord; // 绿色激光坐标
```

**功能**:
- 接收来自树莓派/MaixCam的视觉数据
- 解析激光点坐标信息
- 基于视觉反馈进行位置控制

### 6. 通信系统
- **多路UART**: 支持6路UART通信
- **I2C**: 双路I2C，用于传感器通信
- **环形缓冲区**: 高效的数据缓存机制

## 系统工作流程

### 主程序流程 (main.c)
1. **系统初始化**
   - HAL库初始化
   - 时钟配置
   - 外设初始化 (GPIO, DMA, I2C, TIM, UART)

2. **应用初始化**
   - 任务调度器初始化
   - PID参数初始化
   - 环形缓冲区初始化
   - 步进电机初始化和位置清零

3. **主循环**
   - 执行任务调度器 (`schedule_run()`)
   - 处理各种周期性任务

### 控制逻辑流程
1. **数据采集**: 
   - 树莓派发送视觉数据 → pi_proc()
   - 传感器数据读取 → 各sensor_proc()

2. **数据处理**:
   - 视觉数据解析 → 激光点坐标
   - PID计算 → 控制输出

3. **执行控制**:
   - 步进电机控制 → Step_Motor_Set_Speed_my()
   - 电机驱动输出

## 技术特点

### 优点
1. **模块化设计**: 清晰的分层架构，便于维护和扩展
2. **实时性**: 基于时间片调度，保证实时响应
3. **多传感器融合**: 集成视觉、IMU、编码器等多种传感器
4. **灵活的PID控制**: 支持多种PID配置和控制策略
5. **丰富的通信接口**: 支持多种通信方式

### 可能的应用场景
- **机器人导航**: 基于视觉的路径跟踪
- **精密定位**: 双轴精密位置控制
- **自动化设备**: 工业自动化控制系统
- **竞赛机器人**: 可能是机器人竞赛项目

## 开发建议

### 代码优化
1. **取消注释的代码**: motor_driver.c中大量被注释的代码需要清理
2. **错误处理**: 增强错误处理和异常恢复机制
3. **代码文档**: 增加更详细的中文注释和文档

### 功能扩展
1. **数据记录**: 添加运行数据记录功能
2. **参数调试**: 增加在线参数调试接口
3. **状态监控**: 完善系统状态监控和诊断

### 测试验证
1. **单元测试**: 为关键模块添加单元测试
2. **集成测试**: 验证各模块间的协作
3. **性能测试**: 验证实时性和控制精度

## 总结

这是一个设计良好的嵌入式控制系统，具有完整的传感器融合、PID控制和多任务调度功能。系统架构清晰，模块化程度高，适合用于需要精密控制的机器人或自动化设备。代码质量较高，但仍有优化空间，特别是在错误处理和代码文档方面。