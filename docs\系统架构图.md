# 系统架构图

## 整体系统架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        应用层 (Application Layer)                │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
│  │ HWT101      │  │ Encoder     │  │ Motor       │  │ PID         │ │
│  │ Driver      │  │ Driver      │  │ Driver      │  │ Controller  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘ │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
│  │ Emm_V5      │  │ Hardware    │  │ Vision      │  │ Task        │ │
│  │ Driver      │  │ I2C         │  │ Interface   │  │ Scheduler   │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                      板级支持包 (BSP Layer)                       │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
│  │ UART        │  │ I2C         │  │ GPIO        │  │ Timer       │ │
│  │ BSP         │  │ BSP         │  │ BSP         │  │ BSP         │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘ │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
│  │ Step Motor  │  │ Gray Sensor │  │ OLED        │  │ Key         │ │
│  │ BSP         │  │ BSP         │  │ BSP         │  │ BSP         │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                     硬件抽象层 (HAL Layer)                        │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
│  │ STM32F4xx   │  │ TB6612      │  │ OLED        │  │ Ring        │ │
│  │ HAL Driver  │  │ Driver      │  │ Display     │  │ Buffer      │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                       硬件层 (Hardware Layer)                     │
├─────────────────────────────────────────────────────────────────┤
│                        STM32F407VET6                             │
└─────────────────────────────────────────────────────────────────┘
```

## 数据流架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   传感器     │    │   数据处理   │    │   控制算法   │    │   执行机构   │
│   输入层     │───▶│    融合层    │───▶│    决策层    │───▶│    输出层    │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │                   │
       ▼                   ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│• HWT101     │    │• 坐标转换    │    │• PID控制器   │    │• 步进电机    │
│• 编码器      │    │• 数据滤波    │    │• 路径规划    │    │• 直流电机    │
│• 视觉系统    │    │• 状态估计    │    │• 运动控制    │    │• 显示输出    │
│• 灰度传感器  │    │• 环形缓冲    │    │• 安全保护    │    │• 状态反馈    │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

## 通信架构

```
                    ┌─────────────────────────────────┐
                    │         STM32F407VET6          │
                    │                                 │
    ┌───────────────┼─────────────────────────────────┼───────────────┐
    │               │                                 │               │
    ▼               ▼                                 ▼               ▼
┌─────────┐    ┌─────────┐                      ┌─────────┐    ┌─────────┐
│ UART1   │    │ UART2   │                      │ I2C1    │    │ I2C2    │
│ 调试输出 │    │ Emm_V5  │                      │灰度传感器│    │ OLED    │
└─────────┘    └─────────┘                      └─────────┘    └─────────┘
    │               │                                 │               │
    ▼               ▼                                 ▼               ▼
┌─────────┐    ┌─────────┐                      ┌─────────┐    ┌─────────┐
│ UART3   │    │ UART4   │                      │ TIM1    │    │ TIM3    │
│树莓派通信│    │ Emm_V5  │                      │PWM输出  │    │编码器接口│
└─────────┘    └─────────┘                      └─────────┘    └─────────┘
    │               │                                 │               │
    ▼               ▼                                 ▼               ▼
┌─────────┐    ┌─────────┐                      ┌─────────┐    ┌─────────┐
│ UART5   │    │ UART6   │                      │ TIM4    │    │ GPIO    │
│ HWT101  │    │ 扩展    │                      │通用定时器│    │按键/LED │
└─────────┘    └─────────┘                      └─────────┘    └─────────┘
```

## 任务调度架构

```
                        ┌─────────────────┐
                        │  Main Loop      │
                        │  while(1)       │
                        └─────────┬───────┘
                                  │
                                  ▼
                        ┌─────────────────┐
                        │ schedule_run()  │
                        │ 任务调度器       │
                        └─────────┬───────┘
                                  │
                    ┌─────────────┼─────────────┐
                    │             │             │
                    ▼             ▼             ▼
            ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
            │ uart_proc() │ │ pi_proc()   │ │ 其他任务     │
            │   1ms周期   │ │  20ms周期   │ │  (已注释)   │
            └─────────────┘ └─────────────┘ └─────────────┘
                    │             │             │
                    ▼             ▼             ▼
            ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
            │• 串口数据    │ │• 视觉数据    │ │• OLED显示   │
            │  处理       │ │  解析       │ │• 按键处理   │
            │• 通信管理    │ │• PID计算    │ │• 传感器读取 │
            │• 数据转发    │ │• 电机控制   │ │• 状态监控   │
            └─────────────┘ └─────────────┘ └─────────────┘
```

## 控制系统架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        控制系统架构                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐          │
│  │   视觉系统   │    │   IMU系统   │    │  编码器系统  │          │
│  │             │    │             │    │             │          │
│  │ 红绿激光点   │    │ 角速度/姿态  │    │ 位置/速度    │          │
│  │ 坐标检测     │    │ 角度数据     │    │ 反馈数据     │          │
│  └──────┬──────┘    └──────┬──────┘    └──────┬──────┘          │
│         │                  │                  │                 │
│         └──────────────────┼──────────────────┘                 │
│                            │                                    │
│                            ▼                                    │
│                   ┌─────────────────┐                           │
│                   │   数据融合处理   │                           │
│                   │                 │                           │
│                   │ • 坐标转换       │                           │
│                   │ • 数据滤波       │                           │
│                   │ • 状态估计       │                           │
│                   └─────────┬───────┘                           │
│                             │                                   │
│                             ▼                                   │
│  ┌─────────────────────────────────────────────────────────┐    │
│  │                   PID控制系统                           │    │
│  │                                                         │    │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │    │
│  │  │   X轴PID    │  │   Y轴PID    │  │  速度PID    │     │    │
│  │  │             │  │             │  │             │     │    │
│  │  │ Kp=3        │  │ Kp=3        │  │ Kp=200      │     │    │
│  │  │ Ki=1        │  │ Ki=1        │  │ Ki=50       │     │    │
│  │  │ Kd=0.02     │  │ Kd=0.02     │  │ Kd=0.5      │     │    │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │    │
│  └─────────────────────────┬───────────────────────────────┘    │
│                            │                                   │
│                            ▼                                   │
│                   ┌─────────────────┐                          │
│                   │   执行机构控制   │                          │
│                   │                 │                          │
│                   │ • 步进电机控制   │                          │
│                   │ • TB6612驱动    │                          │
│                   │ • PWM输出       │                          │
│                   └─────────────────┘                          │
│                                                                │
└─────────────────────────────────────────────────────────────────┘
```

## 内存架构

```
┌─────────────────────────────────────────────────────────────────┐
│                    STM32F407VET6 内存分布                        │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  Flash Memory (512KB)                                           │
│  ┌─────────────────────────────────────────────────────────┐    │
│  │ 0x08000000 - 0x0807FFFF                                │    │
│  │                                                         │    │
│  │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │    │
│  │ │ 中断向量表   │ │ 应用程序代码 │ │ 常量数据     │        │    │
│  │ │ (16KB)      │ │ (400KB)     │ │ (96KB)      │        │    │
│  │ └─────────────┘ └─────────────┘ └─────────────┘        │    │
│  └─────────────────────────────────────────────────────────┘    │
│                                                                 │
│  SRAM (128KB + 64KB CCM)                                        │
│  ┌─────────────────────────────────────────────────────────┐    │
│  │ 0x20000000 - 0x2001FFFF (Main SRAM 128KB)             │    │
│  │                                                         │    │
│  │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │    │
│  │ │ 全局变量     │ │ 堆空间       │ │ 栈空间       │        │    │
│  │ │ (32KB)      │ │ (64KB)      │ │ (32KB)      │        │    │
│  │ └─────────────┘ └─────────────┘ └─────────────┘        │    │
│  └─────────────────────────────────────────────────────────┘    │
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐    │
│  │ 0x10000000 - 0x1000FFFF (CCM SRAM 64KB)               │    │
│  │                                                         │    │
│  │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │    │
│  │ │ 环形缓冲区   │ │ PID数据     │ │ 传感器数据   │        │    │
│  │ │ (24KB)      │ │ (16KB)      │ │ (24KB)      │        │    │
│  │ └─────────────┘ └─────────────┘ └─────────────┘        │    │
│  └─────────────────────────────────────────────────────────┘    │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 电源架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        电源分配架构                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│                    ┌─────────────────┐                          │
│                    │   主电源输入     │                          │
│                    │   (12V/24V)     │                          │
│                    └─────────┬───────┘                          │
│                              │                                  │
│              ┌───────────────┼───────────────┐                  │
│              │               │               │                  │
│              ▼               ▼               ▼                  │
│    ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│    │   5V电源    │  │   3.3V电源  │  │  电机电源    │           │
│    │   (逻辑)    │  │   (MCU)     │  │  (12V/24V)  │           │
│    └─────┬───────┘  └─────┬───────┘  └─────┬───────┘           │
│          │                │                │                   │
│          ▼                ▼                ▼                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│  │• OLED显示   │  │• STM32F407  │  │• 步进电机    │             │
│  │• 传感器     │  │• 外设电路   │  │• 直流电机    │             │
│  │• 通信模块   │  │• 时钟电路   │  │• 驱动芯片    │             │
│  └─────────────┘  └─────────────┘  └─────────────┘             │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 接口定义架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        接口定义架构                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  应用层接口 (Application Interface)                              │
│  ┌─────────────────────────────────────────────────────────┐    │
│  │ • HWT101_Create()     - 创建陀螺仪实例                  │    │
│  │ • HWT101_GetGyroZ()   - 获取角速度                     │    │
│  │ • Emm_V5_Pos_Control() - 步进电机位置控制              │    │
│  │ • pid_calc()          - PID计算                        │    │
│  │ • pi_parse_data()     - 视觉数据解析                   │    │
│  └─────────────────────────────────────────────────────────┘    │
│                                                                 │
│  BSP层接口 (BSP Interface)                                      │
│  ┌─────────────────────────────────────────────────────────┐    │
│  │ • schedule_init()     - 任务调度初始化                  │    │
│  │ • schedule_run()      - 任务调度执行                    │    │
│  │ • uart_proc()         - UART处理                       │    │
│  │ • pi_proc()           - 视觉处理                        │    │
│  │ • Step_Motor_Init()   - 步进电机初始化                  │    │
│  └─────────────────────────────────────────────────────────┘    │
│                                                                 │
│  HAL层接口 (HAL Interface)                                      │
│  ┌─────────────────────────────────────────────────────────┐    │
│  │ • HAL_UART_Transmit() - UART发送                       │    │
│  │ • HAL_I2C_Master_Receive() - I2C接收                   │    │
│  │ • HAL_TIM_PWM_Start() - PWM启动                        │    │
│  │ • HAL_GPIO_WritePin() - GPIO写入                       │    │
│  │ • HAL_GetTick()       - 系统时钟                       │    │
│  └─────────────────────────────────────────────────────────┘    │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

这个系统架构图展示了整个项目的层次结构、数据流向、通信方式和控制逻辑，为理解和维护系统提供了清晰的视图。