# 模块功能详细说明

## 应用层模块 (app/)

### 1. HWT101陀螺仪驱动 (hwt101_driver.c/h)

#### 功能概述
HWT101是一款高精度的9轴姿态传感器，提供角速度、加速度和磁场数据。

#### 核心数据结构
```c
typedef struct {
    HWT101_HW_t hw;           // 硬件配置
    HWT101_Data_t data;       // 传感器数据
    HWT101_State_t state;     // 工作状态
    uint8_t enable;           // 使能标志
    uint8_t rx_buffer[HWT101_BUFFER_SIZE];  // 接收缓冲区
    uint8_t rx_index;         // 接收索引
} HWT101_t;
```

#### 主要功能
- **数据读取**: 获取角速度Z轴、偏航角数据
- **配置管理**: 支持传感器参数配置和保存
- **状态监控**: 实时监控传感器工作状态
- **数据校准**: 支持零点校准和数据滤波

#### 接口函数
- `HWT101_Create()`: 创建HWT101实例
- `HWT101_GetGyroZ()`: 获取Z轴角速度
- `HWT101_GetYaw()`: 获取偏航角
- `HWT101_Enable()`: 使能/失能传感器

### 2. Emm_V5步进电机驱动 (Emm_V5.c/h)

#### 功能概述
Emm_V5是一款智能步进电机控制器，支持串口通信控制。

#### 主要功能
- **位置控制**: 绝对位置和相对位置控制
- **速度控制**: 可调速度运行
- **参数配置**: 电机参数在线配置
- **状态监控**: 实时位置、速度、状态反馈

#### 核心命令
- `Emm_V5_Reset_CurPos_To_Zero()`: 当前位置清零
- `Emm_V5_Read_Sys_Params()`: 读取系统参数
- `Emm_V5_Vel_Control()`: 速度控制
- `Emm_V5_Pos_Control()`: 位置控制

### 3. PID控制算法 (mypid.c/h)

#### 控制器配置
```c
pid_t pid_x, pid_y;                    // X/Y轴位置控制
pid_t pid_speed_left, pid_speed_right; // 左右电机速度控制
pid_t pid_location_left, pid_location_right; // 位置外环控制
```

#### PID参数
- **位置环PID**: Kp=3, Ki=1, Kd=0.02
- **速度环PID**: Kp=200, Ki=50, Kd=0.5
- **位置外环PID**: Kp=40, Ki=0, Kd=0.1

#### 控制特性
- **双环控制**: 位置外环 + 速度内环
- **死区处理**: 支持输入死区设置
- **输出限幅**: 防止控制量过大
- **积分限幅**: 防止积分饱和

### 4. 编码器驱动 (encoder_drv.c/h)

#### 功能概述
提供电机位置和速度反馈，支持增量式编码器。

#### 主要功能
- **位置检测**: 实时位置反馈
- **速度计算**: 基于位置差分计算速度
- **方向判断**: 正反转方向识别
- **脉冲计数**: 高精度脉冲计数

### 5. 硬件I2C接口 (hardware_iic.c/h)

#### 功能概述
提供标准化的I2C通信接口，用于传感器通信。

#### 接口函数
- `IIC_ReadByte()`: 读取单字节
- `IIC_ReadBytes()`: 读取多字节
- `IIC_WriteByte()`: 写入单字节
- `IIC_WriteBytes()`: 写入多字节
- `Ping()`: 设备连通性检测

## 板级支持包模块 (bsp/)

### 1. 任务调度器 (schedule.c/h)

#### 调度策略
- **时间片轮转**: 基于系统时钟的时间片调度
- **优先级**: 通过执行周期体现任务优先级
- **非抢占式**: 协作式多任务，任务主动让出CPU

#### 当前任务配置
```c
static schedule_task_t schedule_task[] = {
    {uart_proc, 1, 0},     // UART处理 - 1ms周期（最高优先级）
    {pi_proc, 20, 0}       // 视觉处理 - 20ms周期
};
```

#### 扩展任务（已注释）
- `oled_proc`: OLED显示更新 - 100ms周期
- `motor_proc`: 电机控制 - 20ms周期
- `encoder_proc`: 编码器处理 - 20ms周期
- `key_proc`: 按键处理 - 10ms周期
- `gray_proc`: 灰度传感器 - 20ms周期

### 2. 树莓派通信接口 (pi_bsp.c/h)

#### 数据结构
```c
typedef struct {
    uint8_t laser_id;    // 激光标识 (RED_LASER_ID/GREEN_LASER_ID)
    int x, y;           // 像素坐标
    uint8_t isValid;    // 数据有效标志
} LaserCoord_t;
```

#### 通信协议
- **红色激光**: `red:(x,y)\n`
- **绿色激光**: `gre:(x,y)\n`

#### 控制逻辑
```c
void pi_proc(void) {
    // 基于红绿激光点差值进行PID控制
    pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
    pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
    Step_Motor_Set_Speed_my(-pos_out_x, pos_out_y);
}
```

### 3. 步进电机底层驱动 (step_motor_bsp.c/h)

#### 功能概述
提供步进电机的底层控制接口，封装Emm_V5控制器操作。

#### 主要功能
- **初始化**: 电机参数初始化
- **速度控制**: 双轴速度控制
- **位置控制**: 精确位置控制
- **状态监控**: 电机运行状态监控

### 4. UART通信底层 (uart_bsp.c/h)

#### 功能概述
提供统一的UART通信接口，支持多路UART管理。

#### 通信配置
- **UART1**: 调试输出
- **UART2**: Emm_V5步进电机控制器
- **UART3**: 树莓派通信
- **UART4**: Emm_V5步进电机控制器
- **UART5**: HWT101陀螺仪
- **UART6**: 扩展通信

### 5. 灰度传感器驱动 (gray_bsp.c/h)

#### 功能概述
基于I2C的灰度传感器阵列驱动，用于线路检测。

#### 主要功能
- **数字模式**: 黑白线检测
- **模拟模式**: 灰度值检测
- **阈值设置**: 可调节检测阈值
- **多点检测**: 支持传感器阵列

### 6. OLED显示驱动 (oled_bsp.c/h)

#### 功能概述
提供OLED显示屏的底层驱动和显示接口。

#### 显示功能
- **文本显示**: 支持多种字体
- **图形显示**: 基本图形绘制
- **状态显示**: 系统状态实时显示
- **参数显示**: PID参数、传感器数据显示

### 7. 按键处理 (key_bsp.c/h)

#### 功能概述
提供按键检测和处理功能，支持按键事件。

#### 按键事件
- **按下检测**: key_down事件
- **释放检测**: key_up事件
- **状态保持**: 按键状态记录
- **防抖处理**: 硬件防抖和软件防抖

## 硬件抽象层模块

### 1. TB6612电机驱动 (TB6612/)

#### 芯片特性
- **双路驱动**: 支持两路直流电机
- **PWM控制**: 速度调节
- **方向控制**: 正反转控制
- **电流保护**: 过流保护功能

#### 控制接口
- **PWM信号**: 速度控制
- **DIR信号**: 方向控制
- **STBY信号**: 待机控制

### 2. OLED显示模块 (OLED/)

#### 显示特性
- **分辨率**: 128x64像素
- **通信接口**: I2C/SPI
- **显示内容**: 文本、图形、图像
- **字体支持**: 多种字体大小

### 3. 环形缓冲区 (ringbuffer/)

#### 数据结构
提供高效的FIFO数据缓存机制，用于串口数据缓存。

#### 缓冲区配置
```c
rt_ringbuffer_t ringbuffer_x, ringbuffer_y, ringbuffer_pi;  // 三个独立缓冲区
```

#### 主要功能
- **数据写入**: 循环写入数据
- **数据读取**: FIFO读取数据
- **状态查询**: 缓冲区状态查询
- **溢出保护**: 防止数据溢出

## 模块间依赖关系

### 数据流向
1. **传感器数据采集**:
   - HWT101 → hwt101_bsp → 姿态数据
   - 编码器 → encoder_bsp → 位置/速度数据
   - 灰度传感器 → gray_bsp → 线路数据

2. **视觉数据处理**:
   - 树莓派 → pi_bsp → 激光坐标 → PID控制

3. **控制输出**:
   - PID控制器 → step_motor_bsp → Emm_V5 → 步进电机

### 通信链路
- **I2C总线**: 灰度传感器、OLED显示
- **UART总线**: HWT101、Emm_V5、树莓派通信
- **PWM信号**: TB6612电机驱动
- **GPIO信号**: 按键、LED、控制信号

### 任务调度关系
```
schedule_run() 
├── uart_proc() (1ms)     - 最高优先级，处理所有UART通信
└── pi_proc() (20ms)      - 视觉数据处理和控制输出
```

## 系统配置参数

### PID参数配置
- **位置环**: 响应快速，稳态精度高
- **速度环**: 动态响应好，超调小
- **积分限幅**: 防止积分饱和

### 通信参数
- **波特率**: 根据设备要求配置
- **数据位**: 8位数据位
- **停止位**: 1位停止位
- **校验位**: 无校验

### 定时器配置
- **TIM1**: PWM输出，电机控制
- **TIM3**: 编码器接口
- **TIM4**: 通用定时器

这个模块化的设计使得系统具有良好的可维护性和可扩展性，每个模块职责明确，接口清晰，便于后续的功能扩展和优化。