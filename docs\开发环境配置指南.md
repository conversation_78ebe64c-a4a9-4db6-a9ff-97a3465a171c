# 开发环境配置指南

## 开发环境要求

### 硬件要求
- **操作系统**: Windows 10/11 (64位)
- **内存**: 最小4GB，推荐8GB以上
- **硬盘空间**: 至少5GB可用空间
- **USB接口**: 用于ST-Link调试器连接

### 软件工具链

#### 1. Keil MDK-ARM
- **版本**: V5.32或更高版本
- **许可证**: 需要有效的Keil许可证
- **下载地址**: https://www.keil.com/download/product/
- **安装包**: MDK532.EXE

#### 2. STM32CubeMX
- **版本**: 6.0或更高版本
- **用途**: 硬件配置和代码生成
- **下载地址**: https://www.st.com/en/development-tools/stm32cubemx.html

#### 3. STM32 HAL库
- **版本**: STM32Cube FW_F4 V1.28.2
- **包含内容**: HAL驱动库、CMSIS库、示例代码
- **自动下载**: 通过STM32CubeMX自动管理

#### 4. 调试工具
- **ST-Link Utility**: 用于程序下载和调试
- **串口调试工具**: 推荐使用SSCOM或PuTTY
- **逻辑分析仪**: 可选，用于信号分析

## 环境安装步骤

### 1. 安装Keil MDK-ARM

#### 下载和安装
```bash
1. 访问Keil官网下载MDK-ARM安装包
2. 运行MDK532.EXE，按照向导完成安装
3. 选择安装路径（建议默认路径）
4. 安装完成后启动Keil μVision5
```

#### 许可证配置
```bash
1. 打开Keil μVision5
2. 菜单: File → License Management
3. 输入许可证密钥或申请评估许可证
4. 重启Keil完成激活
```

#### 器件包安装
```bash
1. 菜单: Pack Installer
2. 搜索并安装: Keil.STM32F4xx_DFP.3.0.0
3. 确保STM32F407VE器件支持已安装
```

### 2. 安装STM32CubeMX

#### 安装步骤
```bash
1. 下载STM32CubeMX安装包
2. 运行安装程序，选择安装路径
3. 首次启动时会自动下载固件包
4. 确保STM32F4固件包已下载完成
```

#### 配置Java环境
```bash
# STM32CubeMX需要Java运行环境
1. 确保系统已安装Java 8或更高版本
2. 设置JAVA_HOME环境变量
3. 验证Java安装: java -version
```

### 3. 安装调试工具

#### ST-Link驱动安装
```bash
1. 下载ST-Link驱动程序
2. 连接ST-Link调试器到PC
3. 运行驱动安装程序
4. 验证设备管理器中ST-Link设备正常
```

#### 串口调试工具
```bash
# 推荐SSCOM串口调试助手
1. 下载SSCOM5.13.1或更高版本
2. 解压到任意目录
3. 运行sscom.exe
4. 配置串口参数: 115200-8-N-1
```

## 项目配置

### 1. 导入现有项目

#### 打开Keil工程
```bash
1. 启动Keil μVision5
2. 菜单: Project → Open Project
3. 浏览到项目目录: 2025template (2)/MDK-ARM/
4. 选择并打开: 2025template.uvprojx
```

#### 验证项目配置
```bash
1. 检查目标器件: STM32F407VETx
2. 检查编译器: ARMCC V5.06
3. 检查调试器: ST-Link Debugger
4. 检查包含路径是否正确
```

### 2. 编译配置

#### 编译器设置
```c
// 编译选项配置
Optimization Level: -O3 (高度优化)
Language: C99
Warnings: All warnings enabled
Debug Information: Full debug info
```

#### 链接器设置
```c
// 内存配置
ROM (IROM1): 0x8000000, Size: 0x80000 (512KB)
RAM (IRAM1): 0x20000000, Size: 0x20000 (128KB)
RAM (IRAM2): 0x10000000, Size: 0x10000 (64KB CCM)
```

#### 包含路径配置
```bash
# 确保以下路径已添加到Include Paths
../Core/Inc
../Drivers/STM32F4xx_HAL_Driver/Inc
../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy
../Drivers/CMSIS/Device/ST/STM32F4xx/Include
../Drivers/CMSIS/Include
../bsp
../OLED
../app
../ringbuffer
../TB6612
```

### 3. 调试配置

#### ST-Link调试器配置
```bash
1. 菜单: Options for Target → Debug
2. 选择: ST-Link Debugger
3. 点击Settings配置调试器参数
4. 连接设置: SWD接口，最大时钟频率
```

#### 下载配置
```bash
1. 菜单: Options for Target → Utilities
2. 选择: ST-Link Debugger
3. 勾选: Update Target before Debugging
4. 配置Flash下载算法
```

## 编译和调试

### 1. 编译项目

#### 清理和重建
```bash
1. 菜单: Project → Clean Targets
2. 菜单: Project → Rebuild all target files
3. 检查编译输出窗口，确保无错误
4. 查看编译统计信息
```

#### 常见编译错误处理
```c
// 错误1: 找不到头文件
解决方案: 检查Include Paths配置

// 错误2: 未定义的符号
解决方案: 检查源文件是否已添加到项目

// 错误3: 内存溢出
解决方案: 检查链接器内存配置
```

### 2. 程序下载

#### 下载到目标板
```bash
1. 连接ST-Link调试器到目标板
2. 菜单: Flash → Download
3. 等待下载完成
4. 检查下载状态信息
```

#### 验证程序运行
```bash
1. 按下目标板复位按钮
2. 观察LED指示灯状态
3. 连接串口查看调试输出
4. 验证各功能模块工作正常
```

### 3. 在线调试

#### 启动调试会话
```bash
1. 菜单: Debug → Start/Stop Debug Session
2. 程序自动下载并停在main函数
3. 使用调试工具栏控制程序执行
```

#### 调试功能使用
```bash
# 断点设置
1. 在代码行号处单击设置断点
2. F9键快速设置/取消断点
3. 条件断点: 右键设置断点条件

# 变量监视
1. Watch窗口添加变量监视
2. Memory窗口查看内存内容
3. Call Stack窗口查看函数调用栈

# 程序控制
F5: 运行程序
F10: 单步执行(Step Over)
F11: 单步进入(Step Into)
Shift+F11: 单步退出(Step Out)
```

## 开发工作流程

### 1. 代码开发流程

#### 标准开发流程
```bash
1. 需求分析 → 确定功能需求
2. 模块设计 → 设计软件架构
3. 编码实现 → 编写源代码
4. 单元测试 → 测试各模块功能
5. 集成测试 → 测试系统集成
6. 系统测试 → 完整功能测试
```

#### 代码规范
```c
// 命名规范
函数名: 驼峰命名法 (例: Motor_SetSpeed)
变量名: 下划线命名法 (例: motor_speed)
宏定义: 全大写 (例: MOTOR_SPEED_MAX)
结构体: 类型后缀_t (例: Motor_t)

// 注释规范
/**
 * @brief 函数功能简述
 * @param param1 参数1说明
 * @param param2 参数2说明
 * @retval 返回值说明
 */
```

### 2. 版本控制

#### Git配置建议
```bash
# 初始化Git仓库
git init
git add .
git commit -m "Initial commit"

# .gitignore配置
*.o
*.d
*.crf
*.htm
*.lnp
*.map
*.dep
/MDK-ARM/2025template/
```

#### 分支管理策略
```bash
main分支: 稳定发布版本
develop分支: 开发主分支
feature分支: 功能开发分支
hotfix分支: 紧急修复分支
```

### 3. 测试和验证

#### 单元测试
```c
// 测试框架建议使用Unity
#include "unity.h"

void test_PID_Calculation(void) {
    // 测试PID计算功能
    float result = pid_calc(&pid_x, 100, 120, 0);
    TEST_ASSERT_FLOAT_WITHIN(0.1, expected_value, result);
}
```

#### 硬件在环测试
```bash
1. 连接所有传感器和执行器
2. 运行完整系统测试
3. 验证实时性能指标
4. 记录测试结果和问题
```

## 常见问题解决

### 1. 编译问题

#### 问题: 编译器版本不兼容
```bash
症状: 编译时出现语法错误
解决: 确保使用ARMCC V5.06编译器
检查: Options → Target → Code Generation
```

#### 问题: 链接错误
```bash
症状: 链接时报告内存不足
解决: 检查内存配置和代码大小
优化: 启用编译器优化选项
```

### 2. 调试问题

#### 问题: 无法连接调试器
```bash
症状: ST-Link连接失败
检查: USB连接和驱动程序
解决: 重新安装ST-Link驱动
```

#### 问题: 程序运行异常
```bash
症状: 程序跑飞或死机
检查: 栈溢出、数组越界
工具: 使用调试器单步跟踪
```

### 3. 硬件问题

#### 问题: 串口通信异常
```bash
检查: 波特率、数据位、停止位配置
验证: 使用示波器检查信号质量
解决: 检查硬件连接和电平匹配
```

#### 问题: 电机控制异常
```bash
检查: PWM信号输出和方向控制
验证: 电机驱动电路和供电
测试: 使用万用表检查电压电流
```

## 性能优化建议

### 1. 编译优化
```c
// 编译器优化选项
-O3: 最高级别优化
-Otime: 优化执行速度
-Ospace: 优化代码大小
```

### 2. 代码优化
```c
// 使用内联函数减少函数调用开销
__STATIC_INLINE float fast_sqrt(float x) {
    // 快速平方根算法
}

// 使用查表法替代复杂计算
const float sin_table[360] = {...};
```

### 3. 内存优化
```c
// 使用CCM内存存储频繁访问的数据
__attribute__((section(".ccmram"))) 
static float pid_buffer[1024];

// 合理使用const关键字
const float pi = 3.14159f;
```

这个开发环境配置指南提供了完整的开发环境搭建流程，帮助开发者快速上手项目开发和调试工作。