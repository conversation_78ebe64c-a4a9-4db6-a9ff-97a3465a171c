// 增强版数据转发模式 - 可选方案
#include "pi_bsp.h"

// 增强版解析函数 - 带时间戳和状态信息
int pi_parse_data_enhanced(char *buffer)
{
    if (!buffer)
        return -1;

    int parsed_x, parsed_y;
    int parsed_count;
    uint32_t timestamp = HAL_GetTick(); // 获取系统时间戳

    // 解析红色激光
    if (strncmp(buffer, "red:", 4) == 0)
    {
        parsed_count = sscanf(buffer, "red:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2)
            return -2;

        // 更新全局坐标
        latest_red_laser_coord.x = parsed_x;
        latest_red_laser_coord.y = parsed_y;
        latest_red_laser_coord.isValid = 1;

        // 【增强转发】带时间戳和状态的格式
        my_printf(&huart1, "[%lu] RED:(%d,%d) STATUS:VALID\r\n", 
                  timestamp, parsed_x, parsed_y);
        
        // 【原始转发】保持原格式
        // my_printf(&huart1, "%s", buffer);
    }
    // 解析绿色激光
    else if (strncmp(buffer, "gre:", 4) == 0)
    {
        parsed_count = sscanf(buffer, "gre:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2)
            return -2;

        // 更新全局坐标
        latest_green_laser_coord.x = parsed_x;
        latest_green_laser_coord.y = parsed_y;
        latest_green_laser_coord.isValid = 1;

        // 【增强转发】带时间戳和状态的格式
        my_printf(&huart1, "[%lu] GRE:(%d,%d) STATUS:VALID\r\n", 
                  timestamp, parsed_x, parsed_y);
        
        // 【原始转发】保持原格式
        // my_printf(&huart1, "%s", buffer);
    }
    else
    {
        // 【增强转发】未知数据带时间戳
        my_printf(&huart1, "[%lu] UNKNOWN: %s STATUS:ERROR\r\n", timestamp, buffer);
        return -3;
    }

    return 0;
}

// 定时状态报告函数
void pi_status_report(void)
{
    static uint32_t last_report_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 每5秒发送一次状态报告
    if (current_time - last_report_time >= 5000)
    {
        my_printf(&huart1, "[%lu] STATUS_REPORT: RED(%d,%d):%s GRE(%d,%d):%s\r\n",
                  current_time,
                  latest_red_laser_coord.x, latest_red_laser_coord.y,
                  latest_red_laser_coord.isValid ? "VALID" : "INVALID",
                  latest_green_laser_coord.x, latest_green_laser_coord.y,
                  latest_green_laser_coord.isValid ? "VALID" : "INVALID");
        
        last_report_time = current_time;
    }
}

// 数据统计函数
typedef struct {
    uint32_t red_count;
    uint32_t green_count;
    uint32_t error_count;
    uint32_t total_count;
} DataStats_t;

static DataStats_t data_stats = {0};

void pi_update_stats(int parse_result, char *buffer)
{
    data_stats.total_count++;
    
    if (parse_result == 0)
    {
        if (strncmp(buffer, "red:", 4) == 0)
            data_stats.red_count++;
        else if (strncmp(buffer, "gre:", 4) == 0)
            data_stats.green_count++;
    }
    else
    {
        data_stats.error_count++;
    }
}

void pi_send_stats(void)
{
    my_printf(&huart1, "[STATS] Total:%lu Red:%lu Green:%lu Errors:%lu\r\n",
              data_stats.total_count, data_stats.red_count, 
              data_stats.green_count, data_stats.error_count);
}
