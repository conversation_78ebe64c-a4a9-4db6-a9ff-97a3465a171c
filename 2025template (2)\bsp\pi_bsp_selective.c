// 选择性转发模式 - 可配置的转发策略
#include "pi_bsp.h"

// 转发配置结构体
typedef struct {
    uint8_t forward_red;        // 是否转发红色激光数据
    uint8_t forward_green;      // 是否转发绿色激光数据
    uint8_t forward_unknown;    // 是否转发未知数据
    uint8_t add_timestamp;      // 是否添加时间戳
    uint8_t add_status;         // 是否添加状态信息
    uint16_t forward_interval;  // 转发间隔(ms)，0表示实时转发
} ForwardConfig_t;

// 默认配置：转发所有数据，不添加时间戳，实时转发
static ForwardConfig_t forward_config = {
    .forward_red = 1,
    .forward_green = 1,
    .forward_unknown = 1,
    .add_timestamp = 0,
    .add_status = 0,
    .forward_interval = 0
};

// 转发控制变量
static uint32_t last_forward_time = 0;

// 配置转发参数的函数
void pi_set_forward_config(uint8_t red, uint8_t green, uint8_t unknown, 
                          uint8_t timestamp, uint8_t status, uint16_t interval)
{
    forward_config.forward_red = red;
    forward_config.forward_green = green;
    forward_config.forward_unknown = unknown;
    forward_config.add_timestamp = timestamp;
    forward_config.add_status = status;
    forward_config.forward_interval = interval;
    
    my_printf(&huart1, "Forward config updated: R:%d G:%d U:%d T:%d S:%d I:%d\r\n",
              red, green, unknown, timestamp, status, interval);
}

// 选择性转发函数
void pi_selective_forward(char *buffer, int parse_result)
{
    uint32_t current_time = HAL_GetTick();
    
    // 检查转发间隔
    if (forward_config.forward_interval > 0)
    {
        if (current_time - last_forward_time < forward_config.forward_interval)
            return; // 还未到转发时间
    }
    
    uint8_t should_forward = 0;
    char color_type[10] = "";
    
    // 判断是否需要转发
    if (parse_result == 0)
    {
        if (strncmp(buffer, "red:", 4) == 0)
        {
            should_forward = forward_config.forward_red;
            strcpy(color_type, "RED");
        }
        else if (strncmp(buffer, "gre:", 4) == 0)
        {
            should_forward = forward_config.forward_green;
            strcpy(color_type, "GRE");
        }
    }
    else
    {
        should_forward = forward_config.forward_unknown;
        strcpy(color_type, "UNK");
    }
    
    // 执行转发
    if (should_forward)
    {
        if (forward_config.add_timestamp || forward_config.add_status)
        {
            // 增强格式转发
            my_printf(&huart1, "%s%s%s%s",
                      forward_config.add_timestamp ? "[" : "",
                      forward_config.add_timestamp ? (char*)&current_time : "",
                      forward_config.add_timestamp ? "] " : "",
                      buffer);
            
            if (forward_config.add_status)
            {
                my_printf(&huart1, " STATUS:%s", 
                          parse_result == 0 ? "OK" : "ERROR");
            }
            
            my_printf(&huart1, "\r\n");
        }
        else
        {
            // 原始格式转发
            my_printf(&huart1, "%s", buffer);
        }
        
        last_forward_time = current_time;
    }
}

// 带选择性转发的解析函数
int pi_parse_data_selective(char *buffer)
{
    if (!buffer)
        return -1;

    int parsed_x, parsed_y;
    int parsed_count;
    int result = 0;

    // 解析红色激光
    if (strncmp(buffer, "red:", 4) == 0)
    {
        parsed_count = sscanf(buffer, "red:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2)
        {
            result = -2;
        }
        else
        {
            latest_red_laser_coord.x = parsed_x;
            latest_red_laser_coord.y = parsed_y;
            latest_red_laser_coord.isValid = 1;
            result = 0;
        }
    }
    // 解析绿色激光
    else if (strncmp(buffer, "gre:", 4) == 0)
    {
        parsed_count = sscanf(buffer, "gre:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2)
        {
            result = -2;
        }
        else
        {
            latest_green_laser_coord.x = parsed_x;
            latest_green_laser_coord.y = parsed_y;
            latest_green_laser_coord.isValid = 1;
            result = 0;
        }
    }
    else
    {
        result = -3;
    }
    
    // 执行选择性转发
    pi_selective_forward(buffer, result);
    
    return result;
}

// 转发配置命令处理
void pi_process_forward_command(char *cmd)
{
    if (strncmp(cmd, "FWD_CFG:", 8) == 0)
    {
        int red, green, unknown, timestamp, status, interval;
        if (sscanf(cmd, "FWD_CFG:%d,%d,%d,%d,%d,%d", 
                   &red, &green, &unknown, &timestamp, &status, &interval) == 6)
        {
            pi_set_forward_config(red, green, unknown, timestamp, status, interval);
        }
        else
        {
            my_printf(&huart1, "FWD_CFG format: FWD_CFG:red,green,unknown,timestamp,status,interval\r\n");
        }
    }
    else if (strncmp(cmd, "FWD_STATUS", 10) == 0)
    {
        my_printf(&huart1, "Forward Status: R:%d G:%d U:%d T:%d S:%d I:%d\r\n",
                  forward_config.forward_red, forward_config.forward_green,
                  forward_config.forward_unknown, forward_config.add_timestamp,
                  forward_config.add_status, forward_config.forward_interval);
    }
}
