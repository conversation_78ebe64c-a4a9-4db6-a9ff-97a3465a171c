# 摄像头数据转发系统使用说明

## 🎯 **系统概述**

实现 **MaixCam → STM32 → 电脑** 的数据转发链路，让电脑能够实时接收摄像头识别的激光点坐标数据。

## 🔄 **数据流向**

```
MaixCam摄像头 → UART6(115200) → STM32处理 → UART1(115200) → 电脑串口助手
```

## 🛠️ **实现方案**

### **方案1：直接转发模式（推荐）**

#### **特点**
- ✅ **简单直接**: 原样转发摄像头数据
- ✅ **低延迟**: 最小化处理延迟
- ✅ **格式保持**: 保持原始 `red:(x,y)` 和 `gre:(x,y)` 格式
- ✅ **已实现**: 已修改 `pi_bsp.c` 文件

#### **修改内容**
```c
// 文件: 2025template (2)/bsp/pi_bsp.c
// 在 pi_parse_data() 函数中添加了转发功能

// 红色激光转发
my_printf(&huart1, "%s", buffer);  // 直接转发原始数据

// 绿色激光转发  
my_printf(&huart1, "%s", buffer);  // 直接转发原始数据

// 未知数据转发
my_printf(&huart1, "UNKNOWN: %s", buffer);  // 带标识转发
```

#### **电脑端接收示例**
```
red:(132,134)
gre:(131,133)
red:(130,134)
gre:(129,135)
UNKNOWN: invalid_data
```

### **方案2：增强转发模式**

#### **特点**
- ✅ **带时间戳**: 每条数据带系统时间戳
- ✅ **状态信息**: 显示数据解析状态
- ✅ **统计功能**: 提供数据统计信息
- ✅ **状态报告**: 定期发送系统状态

#### **使用方法**
```c
// 替换 pi_parse_data() 调用为：
int result = pi_parse_data_enhanced(line_buffer);

// 在主循环中添加状态报告：
pi_status_report();  // 每5秒发送一次状态
```

#### **电脑端接收示例**
```
[12345] RED:(132,134) STATUS:VALID
[12346] GRE:(131,133) STATUS:VALID
[12347] UNKNOWN: invalid_data STATUS:ERROR
[17345] STATUS_REPORT: RED(132,134):VALID GRE(131,133):VALID
[STATS] Total:1250 Red:625 Green:600 Errors:25
```

### **方案3：选择性转发模式**

#### **特点**
- ✅ **可配置**: 可选择转发哪种类型的数据
- ✅ **间隔控制**: 可设置转发间隔，避免数据过多
- ✅ **动态配置**: 运行时可修改转发配置
- ✅ **灵活控制**: 支持多种转发格式

#### **配置命令**
```c
// 设置转发配置：红色:1, 绿色:1, 未知:0, 时间戳:1, 状态:0, 间隔:100ms
pi_set_forward_config(1, 1, 0, 1, 0, 100);

// 通过串口命令配置
"FWD_CFG:1,1,0,1,0,100"  // 格式：红,绿,未知,时间戳,状态,间隔

// 查询当前配置
"FWD_STATUS"
```

## 🔧 **硬件连接**

### **STM32端**
- **UART6**: 连接MaixCam (PA11-RX, PA12-TX)
- **UART1**: 连接电脑 (PA9-TX, PA10-RX)

### **电脑端**
- **串口助手**: 115200波特率，8数据位，1停止位，无校验
- **推荐软件**: SSCOM、串口调试助手、PuTTY

## 📊 **性能参数**

### **数据吞吐量**
- **输入速率**: ~50Hz (20ms间隔)
- **单条数据**: ~15字节
- **总吞吐量**: ~750字节/秒
- **转发延迟**: <2ms

### **缓冲容量**
- **接收缓冲**: 64字节环形缓冲区
- **行缓冲**: 128字节
- **发送缓冲**: 512字节

## 🚀 **快速开始**

### **步骤1：编译烧录**
```bash
1. 使用Keil MDK打开工程
2. 编译项目（已修改pi_bsp.c）
3. 烧录到STM32
```

### **步骤2：硬件连接**
```bash
1. MaixCam UART → STM32 UART6
2. STM32 UART1 → 电脑USB转串口
3. 确保波特率都是115200
```

### **步骤3：电脑端设置**
```bash
1. 打开串口助手
2. 设置：115200, 8N1
3. 连接STM32的UART1
4. 开始接收数据
```

### **步骤4：启动MaixCam**
```bash
1. 运行修正后的激光识别程序
2. 确保输出格式：red:(x,y) 和 gre:(x,y)
3. 观察电脑端是否收到数据
```

## 🔍 **调试指南**

### **常见问题**

#### **1. 电脑端收不到数据**
```bash
检查项目：
- STM32 UART1连接是否正确
- 波特率是否匹配（115200）
- STM32程序是否正常运行
- 串口助手设置是否正确
```

#### **2. 数据格式错误**
```bash
检查项目：
- MaixCam输出格式是否正确
- STM32解析是否正常
- 是否有数据丢失或截断
```

#### **3. 数据延迟过大**
```bash
优化方案：
- 检查串口缓冲区设置
- 减少不必要的调试输出
- 优化数据处理流程
```

### **调试命令**

#### **查看系统状态**
```bash
# 通过UART1发送命令到STM32
"FWD_STATUS"     # 查看转发配置
"GET_STATS"      # 查看数据统计
"RESET_STATS"    # 重置统计计数
```

#### **修改转发配置**
```bash
# 只转发红色激光，带时间戳，100ms间隔
"FWD_CFG:1,0,0,1,0,100"

# 转发所有数据，实时，带状态
"FWD_CFG:1,1,1,0,1,0"
```

## 📈 **性能监控**

### **数据统计**
- **接收计数**: 总接收数据包数量
- **解析成功率**: 成功解析的数据比例
- **错误率**: 解析失败的数据比例
- **转发延迟**: 平均转发延迟时间

### **系统监控**
- **缓冲区使用率**: 防止数据溢出
- **CPU使用率**: 确保系统响应性
- **内存使用**: 监控内存泄漏

老板，数据转发系统已经完全准备就绪！推荐使用**方案1（直接转发模式）**，它已经集成到现有代码中，可以直接使用。您只需要将STM32的UART1连接到电脑，就能在串口助手中实时看到MaixCam发送的激光点坐标数据了！
