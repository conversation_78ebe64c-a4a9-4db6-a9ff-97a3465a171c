# 2023E（ZDT 米醋电控板）项目文档

## 项目概述

本项目是一个基于STM32F407VET6微控制器的智能控制系统，主要用于精密位置控制和机器人导航。系统集成了视觉识别、IMU姿态检测、编码器反馈等多种传感器，通过PID控制算法实现高精度的双轴运动控制。

## 文档结构

### 📋 核心文档
- **[工程分析报告](./工程分析报告.md)** - 项目整体架构和技术栈分析
- **[模块功能详细说明](./模块功能详细说明.md)** - 各功能模块的详细说明
- **[系统架构图](./系统架构图.md)** - 系统架构和数据流图表
- **[数据流分析与性能评估](./数据流分析与性能评估.md)** - 性能分析和优化建议

### 🛠️ 开发文档
- **[开发环境配置指南](./开发环境配置指南.md)** - 开发环境搭建和配置
- **[使用说明与维护指南](./使用说明与维护指南.md)** - 系统使用和维护说明

## 快速开始

### 系统要求
- **硬件**: STM32F407VET6开发板
- **开发环境**: Keil MDK-ARM V5.32+
- **调试工具**: ST-Link调试器
- **通信工具**: 串口调试助手

### 快速部署
1. 按照[开发环境配置指南](./开发环境配置指南.md)搭建开发环境
2. 使用Keil打开项目文件：`MDK-ARM/2025template.uvprojx`
3. 编译并下载程序到目标板
4. 连接串口调试工具查看系统运行状态
5. 参考[使用说明与维护指南](./使用说明与维护指南.md)进行系统配置

## 技术特点

### 🎯 核心功能
- **多传感器融合**: 集成视觉、IMU、编码器等传感器
- **精密位置控制**: 基于PID算法的双轴控制系统
- **实时任务调度**: 轻量级多任务调度器
- **视觉跟踪**: 基于激光点的视觉定位系统

### 🏗️ 系统架构
- **分层设计**: 应用层、BSP层、HAL层清晰分离
- **模块化结构**: 各功能模块独立，便于维护扩展
- **标准化接口**: 统一的硬件抽象层接口
- **高效通信**: 多路UART和I2C通信支持

### ⚡ 性能指标
- **控制精度**: ±0.1mm
- **响应时间**: <100ms
- **控制频率**: 50Hz
- **CPU使用率**: ~15%
- **内存使用**: ~5.5KB/192KB

## 硬件配置

### 主要组件
- **主控**: STM32F407VET6 (ARM Cortex-M4, 168MHz)
- **传感器**: HWT101陀螺仪、编码器、灰度传感器
- **执行器**: 步进电机(Emm_V5)、直流电机(TB6612)
- **通信**: 6路UART、2路I2C
- **显示**: OLED显示屏
- **调试**: ST-Link SWD接口

### 接口定义
```
UART1: 调试输出 (115200)
UART2: 步进电机1 (115200)
UART3: 视觉通信 (115200)
UART4: 步进电机2 (115200)
UART5: HWT101传感器 (115200)
UART6: 扩展接口 (115200)

I2C1: 灰度传感器 (400kHz)
I2C2: OLED显示 (400kHz)
```

## 软件架构

### 目录结构
```
2025template (2)/
├── Core/           # STM32 HAL核心文件
├── Drivers/        # STM32 HAL驱动库
├── MDK-ARM/        # Keil工程文件
├── OLED/           # OLED显示模块
├── TB6612/         # TB6612电机驱动
├── app/            # 应用层代码
├── bsp/            # 板级支持包
├── ringbuffer/     # 环形缓冲区
└── docs/           # 项目文档
```

### 关键模块
- **任务调度器**: 基于时间片的协作式调度
- **PID控制器**: 位置环+速度环双环控制
- **传感器驱动**: HWT101、编码器、视觉系统
- **通信管理**: UART/I2C统一接口
- **数据处理**: 环形缓冲区和数据融合

## 开发指南

### 编译环境
1. 安装Keil MDK-ARM V5.32+
2. 安装STM32F4xx器件包
3. 配置ST-Link调试器
4. 导入项目并编译

### 调试方法
1. 使用ST-Link在线调试
2. 串口输出调试信息
3. OLED显示系统状态
4. LED指示运行状态

### 代码规范
- 函数命名：驼峰命名法
- 变量命名：下划线命名法
- 宏定义：全大写
- 注释：详细的功能说明

## 维护支持

### 故障诊断
- 系统启动异常检查
- 通信故障排除
- 传感器故障诊断
- 控制系统调试

### 定期维护
- 传感器校准
- 参数备份
- 性能监控
- 固件更新

### 技术支持
- 详细的技术文档
- 故障排除指南
- 参数配置说明
- 性能优化建议

## 版本信息

- **当前版本**: V1.0
- **发布日期**: 2025年
- **兼容性**: STM32F407VET6
- **开发工具**: Keil MDK-ARM V5.32

## 许可证

本项目遵循相应的开源许可证，具体许可证信息请参考项目根目录下的LICENSE文件。

## 贡献指南

欢迎提交问题报告、功能请求和代码贡献。请遵循以下流程：

1. Fork项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建Pull Request
5. 等待代码审查

## 联系方式

- **项目维护**: ZDT技术团队
- **技术支持**: <EMAIL>
- **文档更新**: 2025年1月

---

**注意**: 本文档基于当前代码分析生成，如有疑问请参考具体的技术文档或联系技术支持。