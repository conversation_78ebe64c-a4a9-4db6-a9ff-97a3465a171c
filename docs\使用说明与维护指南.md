# 使用说明与维护指南

## 系统概述

2023E（ZDT 米醋电控板）是一个基于STM32F407VET6的智能控制系统，主要用于精密位置控制和机器人导航。系统集成了视觉识别、IMU姿态检测、编码器反馈等多种传感器，通过PID控制算法实现高精度的双轴运动控制。

## 硬件连接说明

### 1. 主控板接口定义

#### 电源接口
- **主电源输入**: 12V/24V DC，最大电流3A
- **逻辑电源**: 5V/3.3V，由板载稳压器提供
- **电源指示**: PWR LED (绿色)

#### 通信接口
```
接口     功能           连接设备        波特率
UART1   调试输出        PC串口调试      115200
UART2   步进电机1       Emm_V5控制器    115200  
UART3   视觉通信        树莓派/MaixCam  115200
UART4   步进电机2       Emm_V5控制器    115200
UART5   IMU传感器       HWT101陀螺仪    115200
UART6   扩展通信        预留接口        115200
```

#### I2C接口
```
接口     功能           连接设备        频率
I2C1    传感器通信      灰度传感器阵列   400kHz
I2C2    显示通信        OLED显示屏      400kHz
```

#### 其他接口
- **SWD调试**: ST-Link调试器连接
- **GPIO**: 按键、LED、控制信号
- **PWM**: TB6612电机驱动信号
- **编码器**: TIM3接口，支持AB相编码器

### 2. 外设连接图

```
                    ┌─────────────────────────────────┐
                    │         STM32F407VET6          │
                    │         主控制器               │
                    └─────────────┬───────────────────┘
                                  │
        ┌─────────────────────────┼─────────────────────────┐
        │                         │                         │
        ▼                         ▼                         ▼
┌─────────────┐            ┌─────────────┐            ┌─────────────┐
│   视觉系统   │            │  传感器系统  │            │  执行系统    │
│             │            │             │            │             │
│• 树莓派     │◄──UART3──►│• HWT101     │            │• 步进电机1   │
│• MaixCam    │            │• 编码器     │            │• 步进电机2   │
│• 激光器     │            │• 灰度传感器  │            │• 直流电机    │
└─────────────┘            └─────────────┘            └─────────────┘
        │                         │                         ▲
        │                         │                         │
        └─────────────────────────┼─────────────────────────┘
                                  │
                                  ▼
                        ┌─────────────────┐
                        │   显示与交互     │
                        │                 │
                        │• OLED显示屏     │
                        │• 状态LED        │
                        │• 功能按键       │
                        └─────────────────┘
```

## 系统启动与配置

### 1. 首次启动流程

#### 硬件检查清单
```bash
□ 电源连接正确 (12V/24V)
□ ST-Link调试器连接
□ 串口调试线连接
□ 传感器模块连接
□ 电机驱动器连接
□ 显示屏连接
```

#### 软件启动步骤
```bash
1. 上电后系统自动初始化
2. 观察PWR LED是否点亮 (绿色)
3. 连接串口调试工具 (115200-8-N-1)
4. 观察启动信息输出
5. 等待系统初始化完成 (~3秒)
```

#### 启动信息示例
```
=== ZDT Control System V1.0 ===
[INFO] System initializing...
[INFO] HAL Library initialized
[INFO] Clock configured: 168MHz
[INFO] GPIO initialized
[INFO] UART1-6 initialized
[INFO] I2C1-2 initialized
[INFO] Timer initialized
[INFO] PID controller initialized
[INFO] Step motor initialized
[INFO] Sensor modules initialized
[INFO] System ready!
```

### 2. 参数配置

#### PID参数配置
```c
// 位置环PID参数 (可通过串口调试修改)
pid_x.p = 3.0f;     // 比例系数
pid_x.i = 1.0f;     // 积分系数  
pid_x.d = 0.02f;    // 微分系数

pid_y.p = 3.0f;     // Y轴参数与X轴相同
pid_y.i = 1.0f;
pid_y.d = 0.02f;

// 速度环PID参数
pid_speed_left.p = 200.0f;
pid_speed_left.i = 50.0f;
pid_speed_left.d = 0.5f;
```

#### 传感器校准
```bash
# HWT101陀螺仪校准
1. 将设备放置在水平面上
2. 发送校准命令: "CAL_GYRO"
3. 等待校准完成 (~10秒)
4. 保存校准参数: "SAVE_CONFIG"

# 编码器零点设置
1. 将电机移动到机械零点
2. 发送零点命令: "SET_ZERO"
3. 确认零点设置成功
```

## 操作说明

### 1. 基本操作

#### 手动控制模式
```bash
# 通过串口发送控制命令
"MOVE_X:100"     # X轴移动100个单位
"MOVE_Y:-50"     # Y轴移动-50个单位
"SET_SPEED:50"   # 设置运动速度为50%
"STOP"           # 紧急停止
"HOME"           # 回到原点位置
```

#### 自动控制模式
```bash
# 启动视觉跟踪模式
"AUTO_START"     # 开始自动控制
"AUTO_STOP"      # 停止自动控制
"AUTO_PAUSE"     # 暂停自动控制
"AUTO_RESUME"    # 恢复自动控制
```

#### 参数查询
```bash
"GET_POS"        # 获取当前位置
"GET_SPEED"      # 获取当前速度
"GET_STATUS"     # 获取系统状态
"GET_SENSOR"     # 获取传感器数据
```

### 2. 视觉系统操作

#### 视觉数据格式
```bash
# 输入数据格式 (来自树莓派)
"red:(320,240)"  # 红色激光点坐标
"gre:(300,250)"  # 绿色激光点坐标

# 系统响应
"Parsed RED: X=320, Y=240"
"Parsed GRE: X=300, Y=250"
"Control Output: X=-1.5, Y=2.3"
```

#### 视觉校准步骤
```bash
1. 确保激光器正常工作
2. 调整相机焦距和曝光
3. 设置颜色识别阈值
4. 验证坐标系对应关系
5. 测试控制响应精度
```

### 3. 电机控制操作

#### 步进电机控制
```bash
# 位置控制模式
"STEP_POS:1000,500"    # 移动到位置(1000,500)
"STEP_REL:100,-200"    # 相对移动(100,-200)

# 速度控制模式  
"STEP_VEL:50,30"       # 设置速度(50,30)
"STEP_STOP"            # 停止运动

# 参数设置
"STEP_ACC:1000"        # 设置加速度
"STEP_DEC:800"         # 设置减速度
```

#### 直流电机控制
```bash
# TB6612驱动控制
"DC_SPEED:80,-60"      # 设置左右电机速度
"DC_BRAKE"             # 电机制动
"DC_COAST"             # 电机滑行停止
```

### 4. 传感器数据监控

#### 实时数据显示
```bash
# 启动数据监控
"MONITOR_START"

# 数据输出格式
"GYRO_Z: 0.15 rad/s"
"YAW: 45.2 deg"
"POS_X: 1250 pulse"
"POS_Y: -800 pulse"
"LASER_RED: (320,240)"
"LASER_GRE: (315,245)"
```

#### 数据记录功能
```bash
"LOG_START"            # 开始数据记录
"LOG_STOP"             # 停止数据记录
"LOG_SAVE:filename"    # 保存记录文件
```

## 故障诊断与排除

### 1. 常见故障现象

#### 系统无法启动
```bash
现象: 上电后无任何响应
检查项目:
□ 电源电压是否正确 (12V±0.5V)
□ 电源线连接是否牢固
□ 保险丝是否熔断
□ 主控芯片是否正常

解决方案:
1. 使用万用表检查电源电压
2. 检查电源指示LED状态
3. 重新连接电源线
4. 更换保险丝 (如需要)
```

#### 串口通信异常
```bash
现象: 串口无数据输出或乱码
检查项目:
□ 串口线连接是否正确
□ 波特率设置是否匹配 (115200)
□ 串口驱动是否正常安装

解决方案:
1. 检查串口线TX/RX连接
2. 确认串口参数: 115200-8-N-1
3. 重新安装串口驱动程序
4. 尝试使用其他串口调试工具
```

#### 电机不响应
```bash
现象: 发送控制命令后电机无动作
检查项目:
□ 电机电源是否正常
□ 控制信号连接是否正确
□ 电机驱动器状态是否正常
□ 控制命令格式是否正确

解决方案:
1. 检查电机供电电压
2. 验证控制信号完整性
3. 检查驱动器错误指示
4. 确认命令格式和参数范围
```

### 2. 传感器故障诊断

#### HWT101陀螺仪故障
```bash
故障现象:
- 数据不更新
- 数据异常跳变
- 通信超时

诊断步骤:
1. 检查UART5连接
2. 验证波特率设置
3. 检查传感器供电
4. 发送测试命令

解决方案:
1. 重新连接传感器
2. 重启传感器模块
3. 重新校准传感器
4. 更换传感器 (如需要)
```

#### 视觉系统故障
```bash
故障现象:
- 无视觉数据接收
- 坐标数据异常
- 激光点识别失败

诊断步骤:
1. 检查UART3通信
2. 验证视觉处理程序
3. 检查激光器工作状态
4. 确认相机参数设置

解决方案:
1. 重启视觉处理设备
2. 调整激光器功率
3. 重新校准相机参数
4. 检查环境光照条件
```

### 3. 控制系统故障

#### PID控制异常
```bash
故障现象:
- 系统振荡
- 响应过慢
- 稳态误差大

诊断方法:
1. 监控PID输出值
2. 分析系统响应曲线
3. 检查传感器反馈
4. 验证执行器响应

调整方案:
1. 减小比例系数Kp
2. 增加微分系数Kd
3. 调整积分系数Ki
4. 设置合适的输出限幅
```

## 维护保养

### 1. 日常维护

#### 每日检查项目
```bash
□ 系统启动是否正常
□ 各指示灯状态是否正常
□ 串口通信是否正常
□ 传感器数据是否正常
□ 电机运行是否平稳
□ 系统温度是否正常
```

#### 每周维护项目
```bash
□ 清洁设备表面灰尘
□ 检查连接线缆状态
□ 备份重要配置参数
□ 检查电源电压稳定性
□ 验证控制精度
□ 更新运行日志
```

#### 每月维护项目
```bash
□ 深度清洁设备内部
□ 检查所有连接器
□ 校准传感器精度
□ 更新固件版本
□ 性能基准测试
□ 备份完整系统配置
```

### 2. 预防性维护

#### 环境要求
```bash
工作温度: 0°C ~ 50°C
存储温度: -20°C ~ 70°C
相对湿度: 10% ~ 90% (无凝露)
防护等级: IP20
振动要求: <0.5g (10-55Hz)
```

#### 定期校准
```bash
# 传感器校准周期
HWT101陀螺仪: 每月校准一次
编码器零点: 每周检查一次
视觉系统: 每周校准一次
PID参数: 根据性能需要调整
```

#### 备件管理
```bash
建议备件清单:
□ 保险丝 (2A, 5A)
□ 连接线缆
□ ST-Link调试器
□ HWT101传感器
□ OLED显示屏
□ 按键开关
```

### 3. 故障记录与分析

#### 故障记录表格
```
日期: ________
故障现象: ________________
影响范围: ________________
故障原因: ________________
解决方案: ________________
预防措施: ________________
维修人员: ________________
```

#### 性能监控指标
```bash
关键指标:
- 控制精度: ±0.1mm
- 响应时间: <100ms
- 系统可用率: >99%
- 通信成功率: >99.9%
- 传感器数据有效率: >99%
```

## 安全注意事项

### 1. 电气安全
- 断电后进行维护操作
- 使用合适的防静电措施
- 检查接地连接可靠性
- 避免在潮湿环境中操作

### 2. 机械安全
- 确保电机运动范围内无障碍物
- 设置合适的软件限位
- 紧急停止按钮应随时可用
- 定期检查机械连接紧固度

### 3. 操作安全
- 操作前阅读完整操作手册
- 不要在系统运行时进行调试
- 参数修改前备份原始配置
- 异常情况下立即停止系统

## 技术支持

### 联系方式
- 技术支持邮箱: <EMAIL>
- 技术支持电话: 400-XXX-XXXX
- 在线技术文档: http://docs.zdt-tech.com
- 用户交流群: QQ群 XXXXXXXX

### 服务承诺
- 7×24小时技术支持热线
- 48小时内响应技术问题
- 免费软件更新和技术咨询
- 一年质保，终身维护支持

通过遵循本使用说明与维护指南，可以确保系统长期稳定运行，并及时发现和解决潜在问题。